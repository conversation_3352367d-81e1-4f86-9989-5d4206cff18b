<template>
  <div class="oilPrice">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分产品统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="trend-box">
        <chartBox :title="'趋势分析'">
          <div class="medice-radio">
            <el-radio
              v-for="item in medicine"
              :key="item.value"
              v-model="radio"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </div>
          <TrendAnalysisChart/>
        </chartBox>
      </div>
      <div class="regional-distribution">
        <chartBox :title="'区域分布'"></chartBox>
      </div>
      <div class="sales-situation">
        <chartBox :title="'分区域销售情况'"></chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import ItemCard from "../../components/ItemCard.vue";
import CommonTable from "@/components/comTable/commonTable.vue";
import TrendAnalysisChart from "./trendAnalysisChart/index.vue"
export default {
  name: "oilPrice",
  components: {
    ItemCard,
    CommonTable,
    TrendAnalysisChart
  },
  data() {
    return {
      radio: "",
      cardData: [
        { title: "油气总产量", value: "10000" },
        { title: "天然气总产量", value: "10000" },
        { title: "石油液体总产量", value: "10000" },
      ],
      colums: [
        { label: "作业公司", prop: "company" },
        { label: "油气类型", prop: "type" },
        { label: "本年累计(2025)", prop: "year" },
        { label: "去年同期(2024)", prop: "lastYear" },
        { label: "同比(%)", prop: "yearOnYear" },
      ],
      tableData: [
        {
          company: "作业公司1",
          type: "油气",
          year: "10000",
          lastYear: "10000",
          yearOnYear: "10000",
        },
        {
          company: "作业公司2",
          type: "油气",
          year: "10000",
          lastYear: "10000",
          yearOnYear: "10000",
        },
        {
          company: "作业公司3",
          type: "油气",
          year: "10000",
          lastYear: "10000",
          yearOnYear: "10000",
        },
      ],
      medicine: [
        { label: "油气", value: "1" },
        { label: "天然气", value: "2" },
        { label: "石油液体", value: "3" },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.oilPrice {
  height: calc(100vh - 84px); // 减去Header高度
  display: flex;
  flex-direction: column;
  overflow: auto; // 改为auto，允许整体页面滚动以防内容过多
  padding: 4px; // 添加少量内边距
  .content-up {
    display: flex;
    justify-content: space-between;
    height: 50%; // 调整为50%，为下半部分留出更多空间
    min-height: 320px; // 减少最小高度，确保表格能完整显示
    max-height: 420px; // 减少最大高度限制
    .main-indicators {
      flex: 1;
      margin-right: 10px;

      .card-box {
        display: flex;
        justify-content: space-between;
        margin: 8px 20px; // 减少边距，避免内容溢出
        flex: 1; // 占据剩余空间
        min-height: 0; // 允许flex收缩
        gap: 12px; // 使用gap替代margin-right

        .item-card {
          flex: 1;
          min-width: 0; // 允许收缩
          height: 100%; // 确保卡片填满容器高度
        }
      }
    }
    .statistics-box {
      flex: 1;
      min-width: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
      .table-box {
        margin: 12px 16px; // 减少上下边距
        flex: 1;
        overflow: visible; // 移除滚动条，让表格完整显示
        display: flex;
        flex-direction: column;
      }
    }
  }

  .content-down {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    .trend-box {
      flex: 1;
      .medice-radio {
        margin-top: 8px; // 进一步减少顶部边距
        margin-bottom: 4px; // 减少底部边距
        padding-left: 16px;
        flex-shrink: 0; // 防止单选按钮被压缩

        .el-radio {
          margin-right: 16px;
        }
      }
    }
    .regional-distribution {
      flex: 1;
      margin: 0 10px;
    }
    .sales-situation {
      flex: 1;
    }
  }
}

::v-deep .el-radio__inner {
  border: 1px solid #1783ff;
  background: rgba(23, 131, 255, 0.3);
}

::v-deep .el-radio__label {
  color: #acc2e2;
}
</style>
