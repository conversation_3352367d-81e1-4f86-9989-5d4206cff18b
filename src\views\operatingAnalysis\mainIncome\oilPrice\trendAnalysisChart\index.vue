<template>
  <div class="trend-analysis-chart">
    <div class="chart-header">
      <h3 class="chart-title">近12月实现价格趋势</h3>
    </div>
    <div class="chart-container">
      <div class="chart-box" ref="chartBox"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "TrendAnalysisChart",
  data() {
    return {
      chart: null,
      chartData: {
        // 时间轴数据
        timeData: [
          '23/07', '23/08', '23/09', '23/10', '23/11', '23/12',
          '24/01', '24/02', '24/03', '24/04', '24/05', '24/06'
        ],
        // 石油价格数据（美元/桶）
        oilPriceData: [78.39, 86.09, 95.72, 89.61, 83.90, 77.07, 79.41, 84.93, 86.16, 92.90, 90.48, 139.90],
        // 油气价格数据（美元/桶）
        gasOilPriceData: [48.86, 44.70, 45.63, 53.42, 45.48, 56.59, 44.75, 45.82, 44.19, 54.06, 47.13, 56.20],
        // 天然气价格数据（元/方）
        naturalGasPriceData: [2.06, 1.86, 1.80, 2.25, 1.87, 2.39, 1.83, 1.84, 1.80, 2.22, 1.91, 2.17]
      }
    };
  },
  mounted() {
    this.initChart();
    // 监听窗口大小变化，实现响应式
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器和图表实例
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    initChart() {
      // 初始化ECharts实例
      this.chart = echarts.init(this.$refs.chartBox);

      // 配置图表选项
      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: (params) => {
            let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`;
            params.forEach(param => {
              const unit = param.seriesName === '天然气' ? '元/方' : '美元/桶';
              result += `<div style="margin: 2px 0;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: ${param.value} ${unit}
              </div>`;
            });
            return result;
          }
        },
        legend: {
          data: ['石油价格', '油气价格', '天然气'],
          top: 20,
          textStyle: {
            fontSize: 12,
            color: '#333'
          }
        },
        grid: {
          left: '60px',
          right: '60px',
          top: '80px',
          bottom: '60px',
          containLabel: false
        },
        xAxis: {
          type: 'category',
          data: this.chartData.timeData,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            fontSize: 11,
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          }
        },
        yAxis: [
          {
            // 左侧Y轴 - 石油和油气价格
            type: 'value',
            name: '美元/桶',
            nameTextStyle: {
              color: '#666',
              fontSize: 12
            },
            position: 'left',
            axisLabel: {
              formatter: '{value}',
              fontSize: 11,
              color: '#666'
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#e0e0e0'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#f0f0f0',
                type: 'dashed'
              }
            }
          },
          {
            // 右侧Y轴 - 天然气价格
            type: 'value',
            name: '元/方',
            nameTextStyle: {
              color: '#666',
              fontSize: 12
            },
            position: 'right',
            axisLabel: {
              formatter: '{value}',
              fontSize: 11,
              color: '#666'
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#e0e0e0'
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '石油价格',
            type: 'line',
            yAxisIndex: 0, // 使用左侧Y轴
            data: this.chartData.oilPriceData,
            lineStyle: {
              color: '#4A90E2',
              width: 2
            },
            itemStyle: {
              color: '#4A90E2'
            },
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            label: {
              show: true,
              position: 'top',
              fontSize: 10,
              color: '#4A90E2',
              formatter: '{c}'
            }
          },
          {
            name: '油气价格',
            type: 'line',
            yAxisIndex: 0, // 使用左侧Y轴
            data: this.chartData.gasOilPriceData,
            lineStyle: {
              color: '#7ED321',
              width: 2
            },
            itemStyle: {
              color: '#7ED321'
            },
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            label: {
              show: true,
              position: 'bottom',
              fontSize: 10,
              color: '#7ED321',
              formatter: '{c}'
            }
          },
          {
            name: '天然气',
            type: 'line',
            yAxisIndex: 1, // 使用右侧Y轴
            data: this.chartData.naturalGasPriceData,
            lineStyle: {
              color: '#F5A623',
              width: 2
            },
            itemStyle: {
              color: '#F5A623'
            },
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            label: {
              show: true,
              position: 'top',
              fontSize: 10,
              color: '#F5A623',
              formatter: '{c}'
            }
          }
        ]
      };

      // 设置图表配置
      this.chart.setOption(option);
    },

    handleResize() {
      // 响应式处理
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      if (this.chart && newData) {
        this.chartData = { ...this.chartData, ...newData };
        this.initChart();
      }
    }
  }
};
</script>

<style scoped>
.trend-analysis-chart {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.chart-container {
  padding: 20px;
  height: calc(100% - 80px);
}

.chart-box {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    padding: 15px 15px 8px;
  }

  .chart-title {
    font-size: 14px;
  }

  .chart-container {
    padding: 15px;
  }

  .chart-box {
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .chart-header {
    padding: 10px 10px 5px;
  }

  .chart-title {
    font-size: 12px;
  }

  .chart-container {
    padding: 10px;
  }

  .chart-box {
    min-height: 250px;
  }
}
</style>