<template>
  <div class="trend-analysis-chart">
    <div class="chart-header">
      <h3 class="chart-title">近12月实现价格趋势</h3>
    </div>
    <div class="chart-container">
      <div class="chart-box" ref="chartBox"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import echarsThemeMixins from '@/mixins/echarsThemeMixins.js';

export default {
  name: "TrendAnalysisChart",
  mixins: [echarsThemeMixins],
  data() {
    return {
      chart: null,
      // 项目统一的折线图颜色方案
      lineColors: ["#3FB4FF", "#4EFD84", "#fb9352"],
      chartData: {
        // 时间轴数据
        timeData: [
          '24/08', '24/09', '24/10', '24/11', '24/12', '25/01',
          '25/02', '25/03', '25/04', '25/05', '25/06', '25/07'
        ],
        // 石油价格数据（美元/桶）
        oilPriceData: [78.39, 86.09, 95.72, 89.61, 83.90, 77.07, 79.41, 84.93, 86.16, 92.90, 90.48, 139.90],
        // 油气价格数据（美元/桶）
        gasOilPriceData: [48.86, 44.70, 45.63, 53.42, 45.48, 56.59, 44.75, 45.82, 44.19, 54.06, 47.13, 56.20],
        // 天然气价格数据（元/方）
        naturalGasPriceData: [2.06, 1.86, 1.80, 2.25, 1.87, 2.39, 1.83, 1.84, 1.80, 2.22, 1.91, 2.17]
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化，实现响应式
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器和图表实例
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    initChart() {
      // 初始化ECharts实例
      this.chart = echarts.init(this.$refs.chartBox);

      // 获取当前主题颜色配置
      const themeColors = this.themeColor;

      // 配置图表选项
      const option = {
        backgroundColor: 'transparent', // 使用透明背景，让容器背景色生效
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: themeColors.yAxisLabelColor || '#999'
            }
          },
          backgroundColor: this.curTheme ? 'rgba(12, 15, 41, 0.9)' : 'rgba(255, 255, 255, 0.95)',
          borderColor: themeColors.yAxisLineColor || '#e0e0e0',
          borderWidth: 1,
          textStyle: {
            color: themeColors.subtextColor || '#333',
            fontSize: 12
          },
          formatter: (params) => {
            let result = `<div style="font-weight: bold; margin-bottom: 5px; color: ${themeColors.titleTextColor};">${params[0].axisValue}</div>`;
            params.forEach(param => {
              const unit = param.seriesName === '天然气' ? '元/方' : '美元/桶';
              result += `<div style="margin: 2px 0; color: ${themeColors.subtextColor};">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: ${param.value} ${unit}
              </div>`;
            });
            return result;
          }
        },
        legend: {
          data: ['石油价格', '油气价格', '天然气'],
          top: 8,
          textStyle: {
            fontSize: 11,
            color: themeColors.legendTextColor || '#333'
          }
        },
        grid: {
          left: "3%",
          right: "3%",
          top: "12%",
          bottom: "3%",
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.timeData,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            fontSize: 11,
            color: '#ACC2E2' // 使用与barChart组件一致的颜色
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.2)' // 使用与barChart组件一致的轴线颜色
            }
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: [
          {
            // 左侧Y轴 - 石油和油气价格
            type: 'value',
            name: '美元/桶',
            nameTextStyle: {
              color: '#ACC2E2', // 使用与barChart组件一致的颜色
              fontSize: 12,
              align: 'right'
            },
            position: 'left',
            axisLabel: {
              formatter: '{value}',
              fontSize: 11,
              color: '#ACC2E2' // 使用与barChart组件一致的颜色
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(172, 194, 226, 0.2)' // 使用与barChart组件一致的轴线颜色
              }
            },
            splitLine: {
              lineStyle: {
                color: '#0c2c5a' // 使用与barChart组件一致的分割线颜色
              }
            }
          },
          {
            // 右侧Y轴 - 天然气价格
            type: 'value',
            name: '元/方',
            nameTextStyle: {
              color: '#ACC2E2', // 使用与barChart组件一致的颜色
              fontSize: 12,
              align: 'left'
            },
            position: 'right',
            axisLabel: {
              formatter: '{value}',
              fontSize: 11,
              color: '#ACC2E2' // 使用与barChart组件一致的颜色
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(172, 194, 226, 0.2)' // 使用与barChart组件一致的轴线颜色
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '石油价格',
            type: 'line',
            yAxisIndex: 0, // 使用左侧Y轴
            data: this.chartData.oilPriceData,
            lineStyle: {
              color: this.lineColors[0],
              width: 2
            },
            itemStyle: {
              color: this.lineColors[0]
            },
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            label: {
              show: true,
              position: 'top',
              fontSize: 10,
              color: this.lineColors[0],
              formatter: '{c}'
            }
          },
          {
            name: '油气价格',
            type: 'line',
            yAxisIndex: 0, // 使用左侧Y轴
            data: this.chartData.gasOilPriceData,
            lineStyle: {
              color: this.lineColors[1],
              width: 2
            },
            itemStyle: {
              color: this.lineColors[1]
            },
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            label: {
              show: true,
              position: 'bottom',
              fontSize: 10,
              color: this.lineColors[1],
              formatter: '{c}'
            }
          },
          {
            name: '天然气',
            type: 'line',
            yAxisIndex: 1, // 使用右侧Y轴
            data: this.chartData.naturalGasPriceData,
            lineStyle: {
              color: this.lineColors[2],
              width: 2
            },
            itemStyle: {
              color: this.lineColors[2]
            },
            symbol: 'circle',
            symbolSize: 6,
            smooth: true,
            label: {
              show: true,
              position: 'top',
              fontSize: 10,
              color: this.lineColors[2],
              formatter: '{c}'
            }
          }
        ]
      };

      // 设置图表配置
      this.chart.setOption(option);
    },

    handleResize() {
      // 响应式处理
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      if (this.chart && newData) {
        this.chartData = { ...this.chartData, ...newData };
        this.initChart();
      }
    },

    // 主题适配方法（由echarsThemeMixins调用）
    setOPtions(themeColors) {
      if (this.chart) {
        this.initChart();
      }
    }
  }
};
</script>

<style scoped>
.trend-analysis-chart {
  width: 100%;
  height: 100%;
  background: transparent;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-header {
  padding: 12px 20px 6px;
  transition: all 0.3s ease;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  transition: color 0.3s ease;
}

.chart-container {
  padding: 8px 20px 6px;
  height: calc(100% - 50px);
}

.chart-box {
  width: 100%;
  height: 100%;
  min-height: 320px;
}

/* 深色主题适配 */
html[data-theme="dark"] .trend-analysis-chart {
  background: transparent;
}

html[data-theme="dark"] .chart-title {
  color: #CCE4FF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    padding: 10px 15px 4px;
  }

  .chart-title {
    font-size: 14px;
  }

  .chart-container {
    padding: 6px 15px 4px;
    height: calc(100% - 40px);
  }

  .chart-box {
    min-height: 280px;
  }

  /* 深色主题响应式适配 */
  html[data-theme="dark"] .chart-title {
    color: #CCE4FF;
  }
}

@media (max-width: 480px) {
  .chart-header {
    padding: 8px 10px 3px;
  }

  .chart-title {
    font-size: 12px;
  }

  .chart-container {
    padding: 4px 10px 3px;
    height: calc(100% - 30px);
  }

  .chart-box {
    min-height: 240px;
  }

  /* 深色主题响应式适配 */
  html[data-theme="dark"] .chart-title {
    color: #CCE4FF;
  }
}
</style>