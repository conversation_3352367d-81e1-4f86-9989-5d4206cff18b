<template>
  <div class="sales-chart">
    <div class="chart-box" ref="chartBox"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  name: "salesChart",
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  methods: {
    initChart() {
      let mychart = echarts.init(this.$refs.chartBox);
      const option = {}
      mychart.setOption(option)
    },
  },
};
</script>
<style lang="less" scoped>
.sales-chart {
  width: 100%;
  .chart-box {
    width: 95%;
    height: 300px;
  }
}
</style>
