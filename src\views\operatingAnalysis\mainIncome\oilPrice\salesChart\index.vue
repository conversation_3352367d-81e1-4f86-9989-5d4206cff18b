<template>
  <div class="sales-chart">
    <div class="chart-container">
      <div class="chart-box" ref="chartBox"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "SalesChart",
  data() {
    return {
      chart: null,
      // 项目统一的堆叠柱状图颜色方案
      stackColors: ["#3FB4FF", "#fb9352", "#CCCCCC"],
      chartData: {
        // X轴分类数据
        categories: ['本年累计', '去年同期'],
        // 数据系列
        seriesData: [
          {
            name: '海南区域',
            data: [59, 65], // 本年累计59%，去年同期65%
            stack: 'total'
          },
          {
            name: '香港区域',
            data: [32, 20], // 本年累计32%，去年同期20%
            stack: 'total'
          },
          {
            name: '广东区域',
            data: [9, 15], // 本年累计9%，去年同期15%
            stack: 'total'
          }
        ]
      }
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
    // 监听窗口大小变化，实现响应式
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器和图表实例
    window.removeEventListener('resize', this.handleResize);
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    initChart() {
      // 初始化ECharts实例
      this.chart = echarts.init(this.$refs.chartBox);

      // 配置图表选项
      const option = {
        backgroundColor: 'transparent', // 使用透明背景，让容器背景色生效
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(12, 15, 41, 0.9)',
          borderColor: 'rgba(172, 194, 226, 0.2)',
          borderWidth: 1,
          textStyle: {
            color: '#FEFEFF',
            fontSize: 12
          },
          formatter: (params) => {
            let result = `<div style="font-weight: bold; margin-bottom: 5px; color: #FEFEFF;">${params[0].axisValue}</div>`;
            let total = 0;

            // 计算总值
            params.forEach(param => {
              total += param.value;
            });

            // 显示各区域占比
            params.forEach(param => {
              const percentage = ((param.value / total) * 100).toFixed(0);
              result += `<div style="margin: 2px 0; color: #FEFEFF;">
                <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>
                ${param.seriesName}: ${percentage}%
              </div>`;
            });

            return result;
          }
        },
        legend: {
          data: ['海南区域', '香港区域', '广东区域'],
          top: 8,
          right: '10%',
          textStyle: {
            fontSize: 11,
            color: '#CCE4FF'
          },
          itemWidth: 12,
          itemHeight: 12
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: this.chartData.categories,
          axisLabel: {
            fontSize: 11,
            color: '#ACC2E2'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.2)'
            }
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          name: '占比(%)',
          nameTextStyle: {
            color: '#ACC2E2',
            fontSize: 12,
            align: 'right'
          },
          axisLabel: {
            formatter: '{value}%',
            fontSize: 11,
            color: '#ACC2E2'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(172, 194, 226, 0.2)'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#0c2c5a'
            }
          },
          max: 100 // 设置最大值为100%
        },
        series: this.chartData.seriesData.map((item, index) => ({
          name: item.name,
          type: 'bar',
          stack: item.stack,
          data: item.data,
          itemStyle: {
            color: this.stackColors[index]
          },
          label: {
            show: true,
            position: 'inside',
            formatter: '{c}%',
            fontSize: 12,
            color: '#FFFFFF',
            fontWeight: 'bold'
          },
          barWidth: '60%'
        }))
      };

      // 设置图表配置
      this.chart.setOption(option);
    },

    handleResize() {
      // 响应式处理
      if (this.chart) {
        this.chart.resize();
      }
    },

    // 更新图表数据的方法
    updateChartData(newData) {
      if (this.chart && newData) {
        this.chartData = { ...this.chartData, ...newData };
        this.initChart();
      }
    }
  }
};
</script>

<style scoped>
.sales-chart {
  width: 100%;
  height: 100%;
  background: transparent;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-header {
  padding: 12px 20px 6px;
  transition: all 0.3s ease;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  transition: color 0.3s ease;
}

.chart-container {
  padding: 8px 20px 6px;
  height: calc(100% - 50px);
}

.chart-box {
  width: 100%;
  height: 100%;
  min-height: 320px;
}

/* 深色主题适配 */
html[data-theme="dark"] .sales-chart {
  background: transparent;
}

html[data-theme="dark"] .chart-title {
  color: #CCE4FF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    padding: 10px 15px 4px;
  }

  .chart-title {
    font-size: 14px;
  }

  .chart-container {
    padding: 6px 15px 4px;
    height: calc(100% - 40px);
  }

  .chart-box {
    min-height: 280px;
  }

  /* 深色主题响应式适配 */
  html[data-theme="dark"] .chart-title {
    color: #CCE4FF;
  }
}

@media (max-width: 480px) {
  .chart-header {
    padding: 8px 10px 3px;
  }

  .chart-title {
    font-size: 12px;
  }

  .chart-container {
    padding: 4px 10px 3px;
    height: calc(100% - 30px);
  }

  .chart-box {
    min-height: 240px;
  }

  /* 深色主题响应式适配 */
  html[data-theme="dark"] .chart-title {
    color: #CCE4FF;
  }
}
</style>
